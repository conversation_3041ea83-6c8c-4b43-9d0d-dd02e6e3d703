# version: '3.9'
services:
  ftp-server:
    container_name: ftp-server
    image: drakkan/sftpgo:latest
    restart: unless-stopped
    ports:
      - "8080:8080"
      - "2022:2022"
      - "2121:2121"
      - "12100-12200:12100-12200"
    volumes:
      - D:/AppData/Docker/container.d/ftpgo/data:/srv/sftpgo
      - D:/AppData/Docker/container.d/ftpgo/home:/var/lib/sftpgo
    environment:
      - SFTPGO_FTPD__BINDINGS__0__PORT=2121
      - SFTPGO_FTPD__BINDINGS__0__FORCE_PASSIVE_IP=***************
      - SFTPGO_FTPD__PASSIVE_PORT_RANGE__START=12100
      - SFTPGO_FTPD__PASSIVE_PORT_RANGE__END=12200
    networks:
      - ftp-network

  nginx:
    container_name: ftp-server-nginx-proxy
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "0.0.0.0:80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - ftp-server
    networks:
      - ftp-network

networks:
  ftp-network:
    driver: bridge